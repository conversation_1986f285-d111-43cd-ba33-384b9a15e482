// @ts-ignore
/* eslint-disable */
import request from "@/utils/request.ts";

/** 生成视频总结 根据视频ID，基于textData生成summaryData和summary100并存储到数据库 POST /video/generate-summary */
export async function generateSummary(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.generateSummaryParams,
  options?: { [key: string]: any }
) {
  return request<API.Video>("/video/generate-summary", {
    method: "POST",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
