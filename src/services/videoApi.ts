import request from "@/services/request/request.ts";

// 视频文本数据结构
export interface VideoTextData {
  start_time: number;
  end_time: number;
  text: string;
}

// 视频数据结构
export interface Video {
  video_id: number;
  uploader_id: number;
  video_title: string;
  video_url?: string;
  original_video_name?: string;
  video_cover?: string;
  video_duration?: number;
  video_status: number; // 0上传中, 1处理中, 2完成, 3失败
  is_delete: number;
  create_time: string;
  update_time: string;
  text_data?: VideoTextData[];
  summary_data?: VideoTextData[];
  summary100?: string;
  // 关联数据
  course_name?: string;
  module_name?: string;
  chapter_name?: string;
}

// 章节-视频映射
export interface ChapterVideoMapping {
  id: number;
  chapter_id: number;
  video_id: number;
  sort_order: number;
}

// 视频列表响应
export interface VideoListRes {
  code: number;
  message: string;
  data: {
    list: Video[];
    total: number;
  };
}

// 视频详情响应
export interface VideoDetailRes {
  code: number;
  message: string;
  data: Video;
}

// 视频创建/更新请求
export interface VideoRequest {
  video_title: string;
  video_url?: string;
  original_video_name?: string;
  video_cover?: string;
  video_duration?: number;
  text_data?: VideoTextData[];
  summary_data?: VideoTextData[];
  summary100?: string;
}

// 视频上传请求
export interface VideoUploadRequest {
  chapter_id: number;
  video_title: string;
  video_file: File;
}

/**
 * 获取视频列表
 * @param params 查询参数
 * @returns 视频列表
 */
export function getVideoList(params?: {
  page?: number;
  pageSize?: number;
  keyword?: string;
  course_id?: number;
  module_id?: number;
  chapter_id?: number;
  video_status?: number;
}): Promise<VideoListRes> {
  return request.get('/api/video/list', { params });
}

/**
 * 根据章节ID获取视频列表
 * @param chapterId 章节ID
 * @returns 视频列表
 */
export function getVideosByChapter(chapterId: number): Promise<VideoListRes> {
  return request.get(`/api/chapter/${chapterId}/videos`);
}

/**
 * 获取视频详情
 * @param videoId 视频ID
 * @returns 视频详情
 */
export function getVideoDetail(videoId: number): Promise<VideoDetailRes> {
  return request.get(`/api/video/${videoId}`);
}

/**
 * 创建视频
 * @param data 视频数据
 * @returns 创建结果
 */
export function createVideo(data: VideoRequest): Promise<any> {
  return request.post('/api/video', data);
}

/**
 * 更新视频
 * @param videoId 视频ID
 * @param data 视频数据
 * @returns 更新结果
 */
export function updateVideo(videoId: number, data: VideoRequest): Promise<any> {
  return request.put(`/api/video/${videoId}`, data);
}

/**
 * 删除视频
 * @param videoId 视频ID
 * @returns 删除结果
 */
export function deleteVideo(videoId: number): Promise<any> {
  return request.delete(`/api/video/${videoId}`);
}

/**
 * 上传视频
 * @param data 上传数据
 * @returns 上传结果
 */
export function uploadVideo(data: VideoUploadRequest): Promise<any> {
  const formData = new FormData();
  formData.append('chapter_id', data.chapter_id.toString());
  formData.append('video_title', data.video_title);
  formData.append('video_file', data.video_file);
  
  return request.post('/api/video/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 添加视频到章节
 * @param chapterId 章节ID
 * @param videoId 视频ID
 * @param sortOrder 排序权重
 * @returns 添加结果
 */
export function addVideoToChapter(chapterId: number, videoId: number, sortOrder: number = 0): Promise<any> {
  return request.post('/api/chapter-video-mapping', {
    chapter_id: chapterId,
    video_id: videoId,
    sort_order: sortOrder
  });
}

/**
 * 从章节中移除视频
 * @param chapterId 章节ID
 * @param videoId 视频ID
 * @returns 移除结果
 */
export function removeVideoFromChapter(chapterId: number, videoId: number): Promise<any> {
  return request.delete(`/api/chapter-video-mapping/${chapterId}/${videoId}`);
}
