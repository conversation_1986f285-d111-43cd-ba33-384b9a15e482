// @ts-ignore
/* eslint-disable */
import request from "@/utils/request.ts";

/** 此处后端没有提供注释 GET /videoQuery */
export async function getVideosByCondition(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getVideosByConditionParams,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponse>("/videoQuery", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /videoQuery/page */
export async function getVideosByConditionWithPage(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getVideosByConditionWithPageParams,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponseIPageVideo>("/videoQuery/page", {
    method: "GET",
    params: {
      // pageNum has a default value: 1
      pageNum: "1",
      // pageSize has a default value: 5
      pageSize: "5",
      ...params,
    },
    ...(options || {}),
  });
}
