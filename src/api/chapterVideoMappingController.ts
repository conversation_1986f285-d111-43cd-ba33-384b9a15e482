// @ts-ignore
/* eslint-disable */
import request from "@/utils/request.ts";

/** 此处后端没有提供注释 POST /chapterVideoMapping */
export async function createChapterVideoMapping(
  body: API.ChapterVideoMapping,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponse>("/chapterVideoMapping", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /chapterVideoMapping/${param0} */
export async function getVideoByChapterId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getVideoByChapterIdParams,
  options?: { [key: string]: any }
) {
  const { chapterId: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/chapterVideoMapping/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 DELETE /chapterVideoMapping/${param0} */
export async function deleteChapterVideoMappingById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteChapterVideoMappingByIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/chapterVideoMapping/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}
