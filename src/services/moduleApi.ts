import request from "@/services/request/request.ts";

// 模块数据结构
export interface Module {
  module_id: number;
  module_name: string;
  module_ai_summary?: string;
  is_delete: number;
  create_time: string;
  update_time: string;
}

// 课程-模块映射
export interface CourseModuleMapping {
  id: number;
  course_id: number;
  module_id: number;
  sort_order: number;
}

// 模块列表响应
export interface ModuleListRes {
  code: number;
  message: string;
  data: {
    list: Module[];
    total: number;
  };
}

// 模块详情响应
export interface ModuleDetailRes {
  code: number;
  message: string;
  data: Module;
}

// 模块创建/更新请求
export interface ModuleRequest {
  module_name: string;
  module_ai_summary?: string;
}

/**
 * 获取模块列表
 * @param params 查询参数
 * @returns 模块列表
 */
export function getModuleList(params?: {
  page?: number;
  pageSize?: number;
  keyword?: string;
}): Promise<ModuleListRes> {
  return request.get('/api/module/list', { params });
}

/**
 * 根据课程ID获取模块列表
 * @param courseId 课程ID
 * @returns 模块列表
 */
export function getModulesByCourse(courseId: number): Promise<ModuleListRes> {
  return request.get(`/api/course/${courseId}/modules`);
}

/**
 * 获取模块详情
 * @param moduleId 模块ID
 * @returns 模块详情
 */
export function getModuleDetail(moduleId: number): Promise<ModuleDetailRes> {
  return request.get(`/api/module/${moduleId}`);
}

/**
 * 创建模块
 * @param data 模块数据
 * @returns 创建结果
 */
export function createModule(data: ModuleRequest): Promise<any> {
  return request.post('/api/module', data);
}

/**
 * 更新模块
 * @param moduleId 模块ID
 * @param data 模块数据
 * @returns 更新结果
 */
export function updateModule(moduleId: number, data: ModuleRequest): Promise<any> {
  return request.put(`/api/module/${moduleId}`, data);
}

/**
 * 删除模块
 * @param moduleId 模块ID
 * @returns 删除结果
 */
export function deleteModule(moduleId: number): Promise<any> {
  return request.delete(`/api/module/${moduleId}`);
}

/**
 * 添加模块到课程
 * @param courseId 课程ID
 * @param moduleId 模块ID
 * @param sortOrder 排序权重
 * @returns 添加结果
 */
export function addModuleToCourse(courseId: number, moduleId: number, sortOrder: number = 0): Promise<any> {
  return request.post('/api/course-module-mapping', {
    course_id: courseId,
    module_id: moduleId,
    sort_order: sortOrder
  });
}

/**
 * 从课程中移除模块
 * @param courseId 课程ID
 * @param moduleId 模块ID
 * @returns 移除结果
 */
export function removeModuleFromCourse(courseId: number, moduleId: number): Promise<any> {
  return request.delete(`/api/course-module-mapping/${courseId}/${moduleId}`);
}
