// @ts-ignore
/* eslint-disable */
import request from "@/utils/request.ts";

/** 此处后端没有提供注释 POST /courseModuleMapping */
export async function createCourseModuleMapping(
  body: API.CourseModuleMapping,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponse>("/courseModuleMapping", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /courseModuleMapping/${param0} */
export async function getModuleByCourseId(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getModuleByCourseIdParams,
  options?: { [key: string]: any }
) {
  const { courseId: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/courseModuleMapping/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 DELETE /courseModuleMapping/${param0} */
export async function deleteCourseModuleMappingById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteCourseModuleMappingByIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/courseModuleMapping/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}
