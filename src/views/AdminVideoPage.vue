<template>
  <!-- 主容器 -->
  <div class="admin-video-page">
    <!-- 页面头部区域 -->
    <div class="page-header">
      <h1>视频管理系统</h1>
      <!-- 上传视频按钮 -->
      <a-button type="primary" @click="showUploadModal">
        <UploadOutlined/>
        上传视频
      </a-button>
    </div>

    <!-- 筛选和搜索卡片 -->
    <a-card title="筛选条件" class="filter-card">
      <div class="admin-filter-row">
        <!-- 课程筛选下拉框 -->
        <div class="admin-filter-item">
          <a-select
              v-model:value="filterCourse"
              placeholder="选择课程"
              @change="onCourseFilterChange"
              allow-clear
          >
            <a-select-option
                v-for="course in courses"
                :key="course.courseId"
                :value="course.courseId"
            >
              {{ course.courseName }}
            </a-select-option>
          </a-select>
        </div>

        <!-- 模块筛选下拉框（依赖课程选择） -->
        <div class="admin-filter-item">
          <a-select
              v-model:value="filterModule"
              placeholder="选择模块"
              @change="onModuleFilterChange"
              :disabled="!filterCourse"
              allow-clear
          >
            <a-select-option
                v-for="module in filteredModules"
                :key="module.moduleId"
                :value="module.moduleId"
            >
              {{ module.moduleName }}
            </a-select-option>
          </a-select>
        </div>

        <!-- 章节筛选下拉框（依赖模块选择） -->
        <div class="admin-filter-item">
          <a-select
              v-model:value="filterChapter"
              placeholder="选择章节"
              @change="onFilterChange"
              :disabled="!filterModule"
              allow-clear
          >
            <a-select-option
                v-for="chapter in filteredChapters"
                :key="chapter.chapterId"
                :value="chapter.chapterId"
            >
              {{ chapter.chapterName }}
            </a-select-option>
          </a-select>
        </div>

        <!-- 状态筛选下拉框 -->
        <div class="admin-filter-item">
          <a-select
              v-model:value="filterStatus"
              placeholder="视频状态"
              @change="onFilterChange"
              allow-clear
          >
            <a-select-option :value="0">上传中</a-select-option>
            <a-select-option :value="1">处理中</a-select-option>
            <a-select-option :value="2">完成</a-select-option>
            <a-select-option :value="3">失败</a-select-option>
          </a-select>
        </div>

        <!-- 搜索输入框 -->
        <div class="admin-search-item">
          <a-input
              v-model:value="searchKeyword"
              placeholder="搜索视频标题"
              @pressEnter="loadVideos"
          >
            <template #suffix>
              <a-button type="text" @click="loadVideos">
                <SearchOutlined/>
              </a-button>
            </template>
          </a-input>
        </div>

        <!-- 操作按钮组 -->
        <div class="admin-actions">
          <a-space>
            <a-button @click="resetFilters">重置</a-button>
            <a-button type="primary" @click="loadVideos">查询</a-button>
          </a-space>
        </div>
      </div>
    </a-card>

    <!-- 视频列表表格卡片 -->
    <a-card title="视频列表" class="table-card">
      <a-table
          :columns="columns"
          :data-source="videos"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          row-key="videoId"
      >
        <!-- 自定义表格单元格内容 -->
        <template #bodyCell="{ column, record }">
          <!-- 视频封面列 -->
          <template v-if="column.key === 'video_cover'">
            <img
                :src="record.videoCover || '/default-cover.jpg'"
                :alt="record.videoTitle"
                style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px"
            />
          </template>

          <!-- 视频标题列（带提示） -->
          <template v-if="column.key === 'video_title'">
            <a-tooltip :title="record.videoTitle">
              <span class="video-title">{{ record.videoTitle }}</span>
            </a-tooltip>
          </template>

          <!-- 分类路径列（带提示） -->
          <template v-if="column.key === 'category_path'">
            <a-tooltip :title="getVideoPath(record)">
              <span class="category-path">{{ getVideoPath(record) }}</span>
            </a-tooltip>
          </template>

          <!-- 视频时长列 -->
          <template v-if="column.key === 'video_duration'">
            {{ formatDuration(record.videoDuration) }}
          </template>

          <!-- 视频状态列（带标签颜色） -->
          <template v-if="column.key === 'video_status'">
            <a-tag :color="getStatusColor(record.videoStatus)">
              {{ getStatusText(record.videoStatus) }}
            </a-tag>
          </template>

          <!-- 创建时间列 -->
          <template v-if="column.key === 'create_time'">
            {{ formatDate(record.createTime) }}
          </template>

          <!-- 操作列（包含多个操作按钮） -->
          <template v-if="column.key === 'action'">
            <a-space>
              <!-- 预览按钮 -->
              <a-button type="link" size="small" @click="previewVideo(record)">
                <EyeOutlined/>
                预览
              </a-button>
              <!-- 编辑按钮 -->
              <a-button type="link" size="small" @click="editVideo(record)">
                <EditOutlined/>
                编辑
              </a-button>
              <!-- 删除按钮（带确认对话框） -->
              <a-popconfirm
                  title="确定要删除这个视频吗？"
                  @confirm="deleteVideo(record)"
                  ok-text="确定"
                  cancel-text="取消"
              >
                <a-button type="link" size="small" danger>
                  <DeleteOutlined/>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 上传视频模态框 -->
    <a-modal
        v-model:open="uploadModalVisible"
        title="上传视频"
        width="800px"
        :footer="null"
        :destroyOnClose="true"
    >
      <!-- 上传表单 -->
      <a-form
          ref="uploadFormRef"
          :model="uploadForm"
          :rules="uploadRules"
          layout="vertical"
          @finish="handleUpload"
      >
        <!-- 课程和模块选择行 -->
        <div class="upload-form-row">
          <div class="upload-form-item">
            <!-- 课程选择表单项 -->
            <a-form-item label="课程" name="courseId">
              <a-select
                  v-model:value="uploadForm.courseId"
                  placeholder="选择课程"
                  @change="onUploadCourseChange"
              >
                <a-select-option
                    v-for="course in courses"
                    :key="course.courseId"
                    :value="course.courseId"
                >
                  {{ course.courseName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </div>
          <div class="upload-form-item">
            <!-- 模块选择表单项 -->
            <a-form-item label="模块" name="moduleId">
              <a-select
                  v-model:value="uploadForm.moduleId"
                  placeholder="选择模块"
                  :disabled="!uploadForm.courseId"
                  @change="onUploadModuleChange"
              >
                <a-select-option
                    v-for="module in uploadModules"
                    :key="module.moduleId"
                    :value="module.moduleId"
                >
                  {{ module.moduleName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </div>
        </div>

        <!-- 章节选择行 -->
        <div class="upload-form-row">
          <div class="upload-form-item">
            <!-- 章节选择表单项 -->
            <a-form-item label="章节" name="chapterId">
              <a-select
                  v-model:value="uploadForm.chapterId"
                  placeholder="选择章节"
                  :disabled="!uploadForm.moduleId"
              >
                <a-select-option
                    v-for="chapter in uploadChapters"
                    :key="chapter.chapterId"
                    :value="chapter.chapterId"
                >
                  {{ chapter.chapterName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </div>
        </div>

        <!-- 视频标题输入框 -->
        <a-form-item label="视频标题" name="videoTitle">
          <a-input
              v-model:value="uploadForm.videoTitle"
              placeholder="请输入视频标题"
              :maxlength="200"
              show-count
          />
        </a-form-item>

        <!-- 视频文件上传组件 -->
        <a-form-item label="视频文件" name="videoFile">
          <a-upload
              v-model:file-list="fileList"
              :before-upload="beforeUpload"
              :remove="handleRemove"
              accept="video/*"
              :max-count="1"
          >
            <a-button>
              <UploadOutlined/>
              选择视频文件
            </a-button>
            <div style="margin-top: 8px; color: #666; font-size: 12px">
              支持 MP4、AVI、MOV 等格式，文件大小不超过 500MB
            </div>
          </a-upload>
        </a-form-item>

        <!-- 表单操作按钮 -->
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="uploading">
              上传视频
            </a-button>
            <a-button @click="uploadModalVisible = false">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>


  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, computed} from 'vue'
import {useRouter} from 'vue-router'
import {message} from 'ant-design-vue'
import type {UploadFile, TableColumnsType} from 'ant-design-vue'
import {
  UploadOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import {getAllVideoCourse} from "@/api/videoCourseController.ts";
import {getAllVideoModule} from "@/api/videoModuleController.ts";
import {getAllVideoChapter} from "@/api/videoChapterController.ts";
import {getAllVideo} from "@/api/videoController.ts";
import {getModuleByCourseId} from "@/api/courseModuleMappingController.ts";
import {getChapterByModuleId} from "@/api/moduleChapterMappingController.ts";
import {getVideosByConditionWithPage} from "@/api/videoQueryController.ts";

/**
 * 数据类型定义
 */

// 课程数据结构
interface Course {
  courseId: number
  courseName: string
  courseAiSummary?: string
}

// 模块数据结构
interface Module {
  moduleId: number
  moduleName: string
  moduleAiSummary?: string
}

// 章节数据结构
interface Chapter {
  chapterId: number
  chapterName: string
  chapterAiSummary?: string
}


// 视频数据结构
interface Video {
  videoId: number
  uploaderId: number
  videoTitle: string
  videoUrl: string
  originalVideoName: string
  videoCover: string
  videoDuration: number
  videoStatus: number
  createTime: string
  updateTime: string
  textData?: any[]
  summaryData?: any[]
  summary100?: string
  // 关联数据
  courseName?: string
  moduleName?: string
  chapterName?: string
}

/**
 * 路由和响应式数据
 */
const router = useRouter()
const loading = ref(false) // 表格加载状态
const uploading = ref(false) // 上传按钮加载状态
const courses = ref<Course[]>([]) // 课程列表
const modules = ref<Module[]>([]) // 模块列表
const chapters = ref<Chapter[]>([]) // 章节列表
const videos = ref<Video[]>([]) // 视频列表

/**
 * 筛选条件
 */
const filterCourse = ref<number | undefined>() // 当前筛选的课程ID
const filterModule = ref<number | undefined>() // 当前筛选的模块ID
const filterChapter = ref<number | undefined>() // 当前筛选的章节ID
const filterStatus = ref<number | undefined>() // 当前筛选的状态
const searchKeyword = ref('') // 搜索关键词

/**
 * 计算属性
 */
// 根据选中的课程筛选模块
const filteredModules = computed(() => {
  return modules.value
})

// 根据选中的模块筛选章节
const filteredChapters = computed(() => {
  return chapters.value
})

/**
 * 分页配置
 */
const pagination = reactive({
  current: 1, // 当前页码
  pageSize: 10, // 每页条数
  total: 0, // 总条数
  showSizeChanger: true, // 显示每页条数选择器
  showQuickJumper: true, // 显示快速跳转
  showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条` // 显示总数信息
})

/**
 * 表格列配置
 */
const columns: TableColumnsType = [
  {
    title: '封面',
    key: 'video_cover',
    width: 80,
    align: 'center'
  },
  {
    title: '视频标题',
    key: 'video_title',
    width: 200,
    ellipsis: true
  },
  {
    title: '分类路径',
    key: 'category_path',
    width: 250,
    ellipsis: true
  },
  {
    title: '时长',
    key: 'video_duration',
    width: 80,
    align: 'center'
  },
  {
    title: '状态',
    key: 'video_status',
    width: 80,
    align: 'center'
  },
  {
    title: '上传时间',
    key: 'create_time',
    width: 150,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    align: 'center',
    fixed: 'right'
  }
]

/**
 * 上传相关数据
 */
const uploadModalVisible = ref(false) // 上传模态框显示状态
const uploadFormRef = ref() // 上传表单引用
const fileList = ref<UploadFile[]>([]) // 上传文件列表
const uploadModules = ref<Module[]>([]) // 上传表单中的模块列表
const uploadChapters = ref<Chapter[]>([]) // 上传表单中的章节列表

// 上传表单数据
const uploadForm = reactive({
  courseId: undefined as number | undefined,
  moduleId: undefined as number | undefined,
  chapterId: undefined as number | undefined,
  videoTitle: '',
  videoFile: null as File | null
})

// 上传表单验证规则
const uploadRules = {
  courseId: [{required: true, message: '请选择课程'}],
  moduleId: [{required: true, message: '请选择模块'}],
  chapterId: [{required: true, message: '请选择章节'}],
  videoTitle: [
    {required: true, message: '请输入视频标题'},
    {max: 200, message: '标题长度不能超过200个字符'}
  ],
  videoFile: [{required: true, message: '请选择视频文件'}]
}


/**
 * 生命周期钩子
 */
onMounted(() => {
  loadCourses()
  loadVideos()
})

/**
 * 数据加载方法
 */

// 加载课程数据
const loadCourses = async () => {
  try {
    const response = await getAllVideoCourse()
    if (response.data.code === 0) {
      courses.value = response.data.data as Course[]
    }
  } catch (error) {
    console.error('加载课程失败:', error)
  }
}

// 加载模块数据（根据课程ID）
const loadModules = async (courseId: number) => {
  try {
    const response = await getModuleByCourseId({courseId})
    if (response.data.code === 0) {
      const allModules = response.data.data as Module[]
      modules.value = allModules as Module[]
    } else {
      modules.value = []
    }
  } catch (error) {
    console.error('加载模块失败:', error)
    modules.value = []
  }
}

// 加载章节数据（根据模块ID）
const loadChapters = async (moduleId: number) => {
  try {
    const response = await getChapterByModuleId({moduleId})
    if (response.data.code === 0) {
      const allChapters = response.data.data as Chapter[]
      chapters.value = allChapters as Chapter[]
    } else {
      chapters.value = []
    }
  } catch (error) {
    console.error('加载章节失败:', error)
    chapters.value = []
  }
}


// 加载视频数据
const loadVideos = async () => {
  loading.value = true
  try {
    // 构建请求参数
    const params: any = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    }
    if (filterCourse.value !== null && filterCourse.value !== undefined) {
      params.courseId = filterCourse.value
    }
    if (filterModule.value !== null && filterModule.value !== undefined) {
      params.moduleId = filterModule.value
    }
    if (filterChapter.value !== null && filterChapter.value !== undefined) {
      params.chapterId = filterChapter.value
    }
    if (filterStatus.value !== null && filterStatus.value !== undefined) {
      params.videoStatus = filterStatus.value
    }
    if (searchKeyword.value) {
      params.videoTitle = searchKeyword.value
    }

    const response = await getVideosByConditionWithPage(params)
    if (response.data.code === 0) {
      const result = response.data.data
      videos.value = result.records as Video[]
      pagination.total = result.total
    } else {
      videos.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载视频失败:', error)
    videos.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

/**
 * 筛选事件处理
 */

// 课程筛选变化事件
const onCourseFilterChange = (value: number) => {
  filterModule.value = undefined
  filterChapter.value = undefined
  modules.value = []
  chapters.value = []

  if (value) {
    loadModules(value)
  }
  pagination.current = 1
  loadVideos()
}

// 模块筛选变化事件
const onModuleFilterChange = (value: number) => {
  filterChapter.value = undefined
  chapters.value = []

  if (value) {
    loadChapters(value)
  }
  pagination.current = 1
  loadVideos()
}

// 筛选条件变化时重新加载视频
const onFilterChange = () => {
  pagination.current = 1
  loadVideos()
}

// 重置所有筛选条件
const resetFilters = () => {
  filterCourse.value = undefined
  filterModule.value = undefined
  filterChapter.value = undefined
  filterStatus.value = undefined
  searchKeyword.value = ''
  pagination.current = 1
  loadVideos()
}

/**
 * 表格事件处理
 */

// 处理表格分页变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadVideos()
}

/**
 * 上传相关方法
 */

// 显示上传模态框
const showUploadModal = () => {
  uploadModalVisible.value = true
  resetUploadForm()
}

// 重置上传表单
const resetUploadForm = () => {
  uploadForm.courseId = undefined
  uploadForm.moduleId = undefined
  uploadForm.chapterId = undefined
  uploadForm.videoTitle = ''
  uploadForm.videoFile = null
  fileList.value = []
  uploadModules.value = []
  uploadChapters.value = []
}

// 上传表单课程变化事件
const onUploadCourseChange = async (value: number) => {
  uploadForm.moduleId = undefined
  uploadForm.chapterId = undefined
  uploadChapters.value = []

  if (value) {
    await loadModules(value)
    uploadModules.value = modules.value
  }
}

// 上传表单模块变化事件
const onUploadModuleChange = async (value: number) => {
  uploadForm.chapterId = undefined

  if (value) {
    await loadChapters(value)
    uploadChapters.value = chapters.value
  }
}

// 上传前校验
const beforeUpload = (file: File) => {
  const isVideo = file.type.startsWith('video/')
  if (!isVideo) {
    message.error('只能上传视频文件!')
    return false
  }

  const isLt500M = file.size / 1024 / 1024 < 500
  if (!isLt500M) {
    message.error('视频文件大小不能超过 500MB!')
    return false
  }

  uploadForm.videoFile = file
  return false // 阻止自动上传
}

// 移除上传文件
const handleRemove = () => {
  uploadForm.videoFile = null
  return true
}

// 处理上传提交
const handleUpload = async () => {
  if (!uploadForm.videoFile) {
    message.error('请选择视频文件')
    return
  }

  uploading.value = true
  try {
    // 模拟上传API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    message.success('视频上传成功!')
    uploadModalVisible.value = false
    loadVideos()
  } catch (error) {
    message.error('视频上传失败')
  } finally {
    uploading.value = false
  }
}

/**
 * 视频操作方法
 */

// 预览视频
const previewVideo = (video: Video) => {
  // 跳转到专门的视频播放页面
  router.push(`/video/${video.videoId}`)
}

// 编辑视频
const editVideo = (video: Video) => {
  // 这里可以打开编辑模态框
  message.info('编辑功能待实现')
}

// 删除视频
const deleteVideo = async (video: Video) => {
  try {
    // 模拟删除API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    message.success('视频删除成功!')
    loadVideos()
  } catch (error) {
    message.error('视频删除失败')
  }
}

/**
 * 工具函数
 */

// 格式化视频时长
const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

// 格式化日期
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 获取视频路径
const getVideoPath = (video: Video) => {
  return `${video.courseName || '未分类'} > ${video.moduleName || '未分类'} > ${video.chapterName || '未分类'}`
}

// 获取状态标签颜色
const getStatusColor = (status: number) => {
  const colors = {
    0: 'processing', // 上传中
    1: 'warning',    // 处理中
    2: 'success',    // 完成
    3: 'error'       // 失败
  }
  return colors[status as keyof typeof colors] || 'default'
}

// 获取状态文本
const getStatusText = (status: number) => {
  const texts = {
    0: '上传中',
    1: '处理中',
    2: '完成',
    3: '失败'
  }
  return texts[status as keyof typeof texts] || '未知'
}
</script>

<style scoped>
/* 主容器样式 */
.admin-video-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
}

.page-header h1 {
  margin: 0;
  color: #1890ff;
}

/* 筛选卡片样式 */
.filter-card {
  margin-bottom: 16px;
}

.admin-filter-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: center;
}

.admin-filter-item {
  flex: 1;
  min-width: 150px;
}

.admin-filter-item .ant-select {
  width: 100%;
}

.admin-search-item {
  flex: 2;
  min-width: 250px;
}

.admin-actions {
  flex-shrink: 0;
}

.upload-form-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.upload-form-item {
  flex: 1;
  min-width: 250px;
}

/* 表格卡片样式 */
.table-card {
  background: white;
}

/* 视频标题样式（带省略号） */
.video-title {
  display: block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分类路径样式（带省略号） */
.category-path {
  display: block;
  max-width: 230px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
  color: #666;
}

/* 表格单元格内边距调整 */
:deep(.ant-table-tbody > tr > td) {
  padding: 12px 8px;
}

/* 上传列表样式调整 */
:deep(.ant-upload-list) {
  margin-top: 8px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .admin-filter-row {
    flex-direction: column;
  }

  .admin-filter-item,
  .admin-search-item {
    min-width: auto;
  }

  .upload-form-row {
    flex-direction: column;
  }

  .upload-form-item {
    min-width: auto;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}
</style>