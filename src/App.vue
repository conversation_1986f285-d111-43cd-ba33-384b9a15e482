<script setup lang="ts">
import {ref} from 'vue'
import {useRouter, useRoute} from 'vue-router'

const router = useRouter()
const route = useRoute()

const selectedKeys = ref([route.path])

const menuItems = [
  {
    key: '/',
    label: '首页',
    title: '首页'
  },
  {
    key: '/user/videos',
    label: '视频学习',
    title: '视频学习'
  },
  {
    key: '/admin/videos',
    label: '视频管理',
    title: '视频管理'
  },
  {
    key: '/video/1',
    label: '视频播放测试',
    title: '视频播放测试'
  }
]

const handleMenuClick = ({key}: { key: string }) => {
  router.push(key)
  selectedKeys.value = [key]
}
</script>

<template>
  <div id="app">
    <!-- 顶部导航 -->
    <a-layout>
      <a-layout-header style="background: #fff; padding: 0 50px; box-shadow: 0 2px 8px rgba(0,0,0,0.1)">
        <div style="display: flex; align-items: center; height: 64px">
          <div style="font-size: 20px; font-weight: bold; color: #1890ff; margin-right: 50px">
            蜗牛AI教育平台
          </div>
          <a-menu
              mode="horizontal"
              :selected-keys="selectedKeys"
              @click="handleMenuClick"
              style="border-bottom: none; flex: 1"
          >
            <a-menu-item v-for="item in menuItems" :key="item.key">
              {{ item.label }}
            </a-menu-item>
          </a-menu>
        </div>
      </a-layout-header>

      <a-layout-content>
        <router-view/>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<style>
#app {
  min-height: 100vh;
}

body {
  margin: 0;
  padding: 0;
}
</style>
