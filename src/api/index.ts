// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as chapterVideoMappingController from "./chapterVideoMappingController";
import * as courseModuleMappingController from "./courseModuleMappingController";
import * as moduleChapterMappingController from "./moduleChapterMappingController";
import * as shipinzongjiejiekou from "./shipinzongjiejiekou";
import * as videoChapterController from "./videoChapterController";
import * as videoController from "./videoController";
import * as videoCourseController from "./videoCourseController";
import * as videoModuleController from "./videoModuleController";
import * as videoQueryController from "./videoQueryController";
export default {
  videoController,
  videoModuleController,
  videoCourseController,
  videoChapterController,
  shipinzong<PERSON><PERSON><PERSON><PERSON>,
  moduleChapterMappingController,
  courseModuleMappingController,
  chapterVideoMappingController,
  videoQueryController,
};
