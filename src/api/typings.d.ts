declare namespace API {
  type BaseResponse = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
  };

  type ChapterVideoMapping = {
    id?: number;
    chapterId?: number;
    videoId?: number;
    sortOrder?: number;
  };

  type CourseModuleMapping = {
    id?: number;
    courseId?: number;
    moduleId?: number;
    sortOrder?: number;
  };

  type deleteChapterVideoMappingByIdParams = {
    id: number;
  };

  type deleteCourseModuleMappingByIdParams = {
    id: number;
  };

  type deleteModuleChapterMappingByIdParams = {
    id: number;
  };

  type deleteVideoByIdParams = {
    id: number;
  };

  type deleteVideoChapterByIdParams = {
    id: number;
  };

  type deleteVideoCourseByIdParams = {
    id: number;
  };

  type deleteVideoModuleByIdParams = {
    id: number;
  };

  type getVideoByIdParams = {
    id: number;
  };

  type getVideoChapterByIdParams = {
    id: number;
  };

  type getVideoCourseByIdParams = {
    id: number;
  };

  type getVideoModuleByIdParams = {
    id: number;
  };

  type ModuleChapterMapping = {
    id?: number;
    moduleId?: number;
    chapterId?: number;
    sortOrder?: number;
  };

  type MyText = {
    startTime?: number;
    endTime?: number;
    text?: string;
  };

  type Video = {
    videoId?: number;
    uploaderId?: number;
    videoTitle?: string;
    videoUrl?: string;
    originalVideoName?: string;
    videoCover?: string;
    videoDuration?: number;
    videoStatus?: number;
    isDelete?: number;
    createTime?: string;
    updateTime?: string;
    textData?: MyText[];
    summaryData?: MyText[];
    summary100?: string;
  };

  type VideoChapter = {
    chapterId?: number;
    chapterName?: string;
    chapterAiSummary?: string;
    isDelete?: number;
    createTime?: string;
    updateTime?: string;
  };

  type VideoCourse = {
    courseId?: number;
    courseName?: string;
    courseAiSummary?: string;
    isDelete?: number;
    createTime?: string;
    updateTime?: string;
  };

  type VideoModule = {
    moduleId?: number;
    moduleName?: string;
    moduleAiSummary?: string;
    isDelete?: number;
    createTime?: string;
    updateTime?: string;
  };
}
