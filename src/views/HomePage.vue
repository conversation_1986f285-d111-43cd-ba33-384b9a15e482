<template>
  <!-- 主页面容器：包含整个首页的所有内容 -->
  <div class="home-page">
    <!-- 英雄区域：展示平台名称和欢迎信息 -->
    <div class="hero-section">
      <h1>欢迎来到蜗牛AI教育平台</h1>
      <p>选择您的身份开始使用平台功能</p>
    </div>

    <!-- 角色卡片容器：展示不同用户角色的选项卡片 -->
    <div class="cards-container">
      <!-- 弹性布局容器：使用CSS Flexbox实现响应式卡片布局 -->
      <div class="cards-flex">
        <!-- 学生角色卡片：可点击，有悬停效果 -->
        <a-card
            hoverable
            class="role-card"
            @click="goToUserPage"
        >
          <!-- 卡片封面区域：显示学生图标 -->
          <template #cover>
            <div class="card-cover user-cover">
              <UserOutlined style="font-size: 64px; color: #1890ff"/>
            </div>
          </template>
          <!-- 卡片元数据：包含标题和描述 -->
          <a-card-meta
              title="学生用户"
              description="浏览和观看教学视频，享受AI智能总结功能"
          />
          <!-- 卡片功能列表：展示学生用户可使用的功能 -->
          <div class="card-features">
            <p>✓ 分类浏览视频</p>
            <p>✓ 智能搜索功能</p>
            <p>✓ AI视频总结</p>
            <p>✓ 在线视频播放</p>
          </div>
        </a-card>

        <!-- 管理员角色卡片：可点击，有悬停效果 -->
        <a-card
            hoverable
            class="role-card"
            @click="goToAdminPage"
        >
          <!-- 卡片封面区域：显示管理员图标 -->
          <template #cover>
            <div class="card-cover admin-cover">
              <SettingOutlined style="font-size: 64px; color: #52c41a"/>
            </div>
          </template>
          <!-- 卡片元数据：包含标题和描述 -->
          <a-card-meta
              title="管理员"
              description="管理视频内容，上传新视频，监控系统状态"
          />
          <!-- 卡片功能列表：展示管理员可使用的功能 -->
          <div class="card-features">
            <p>✓ 视频上传管理</p>
            <p>✓ 内容分类管理</p>
            <p>✓ 状态监控</p>
            <p>✓ 批量操作</p>
          </div>
        </a-card>
      </div>
    </div>

    <!-- 快速链接区域：提供直接进入不同模式的按钮 -->
    <div class="quick-links">
      <h3>快速链接</h3>
      <!-- 按钮组：使用Ant Design的Space组件实现按钮间距 -->
      <a-space size="large">
        <a-button type="primary" @click="goToUserPage">
          <UserOutlined/>
          进入学习模式
        </a-button>
        <a-button type="default" @click="goToAdminPage">
          <SettingOutlined/>
          进入管理模式
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
// 导入路由工具和图标组件
import {useRouter} from 'vue-router'
import {UserOutlined, SettingOutlined} from '@ant-design/icons-vue'

// 初始化路由实例
const router = useRouter()

/**
 * 导航到学生用户页面
 * 跳转到用户视频列表页面
 */
const goToUserPage = () => {
  router.push('/user/videos')
}

/**
 * 导航到管理员页面
 * 跳转到管理员视频管理页面
 */
const goToAdminPage = () => {
  router.push('/admin/videos')
}
</script>

<style scoped>
/* 主页面样式：设置背景渐变和基本布局 */
.home-page {
  padding: 40px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: white;
}

/* 英雄区域样式：设置文本对齐和间距 */
.hero-section {
  text-align: center;
  margin-bottom: 60px;
}

.hero-section h1 {
  font-size: 48px;
  margin-bottom: 16px;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-section p {
  font-size: 20px;
  opacity: 0.9;
}

/* 卡片容器样式：设置底部间距 */
.cards-container {
  margin-bottom: 60px;
}

.cards-flex {
  display: flex;
  justify-content: center;
  gap: 32px;
  flex-wrap: wrap;
}

/* 角色卡片样式：设置高度、过渡效果和鼠标指针 */
.role-card {
  height: 400px;
  transition: all 0.3s ease;
  cursor: pointer;
  flex: 1 1 300px;
  max-width: 400px;
}

/* 卡片悬停效果：上移并增加阴影 */
.role-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
}

/* 卡片封面样式：设置高度和居中对齐 */
.card-cover {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

/* 学生卡片封面背景 */
.user-cover {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

/* 管理员卡片封面背景 */
.admin-cover {
  background: linear-gradient(135deg, #f1f8e9 0%, #c8e6c9 100%);
}

/* 卡片功能列表样式：设置间距和边框 */
.card-features {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.card-features p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

/* 快速链接区域样式：设置文本对齐 */
.quick-links {
  text-align: center;
}

.quick-links h3 {
  margin-bottom: 24px;
  color: white;
  font-size: 24px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .cards-flex {
    flex-direction: column;
    align-items: center;
  }

  .role-card {
    max-width: 100%;
    flex: 1 1 auto;
  }

  .hero-section h1 {
    font-size: 36px;
  }

  .hero-section p {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .home-page {
    padding: 20px 16px;
  }

  .hero-section h1 {
    font-size: 28px;
  }

  .hero-section p {
    font-size: 16px;
  }

  .cards-container {
    margin-bottom: 40px;
  }
}

</style>
