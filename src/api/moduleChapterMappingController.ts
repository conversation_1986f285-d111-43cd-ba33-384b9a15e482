// @ts-ignore
/* eslint-disable */
import request from "@/utils/request.ts";

/** 此处后端没有提供注释 POST /moduleChapterMapping */
export async function createModuleChapterMapping(
  body: API.ModuleChapterMapping,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponse>("/moduleChapterMapping", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 DELETE /moduleChapterMapping/${param0} */
export async function deleteModuleChapterMappingById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteModuleChapterMappingByIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/moduleChapterMapping/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}
