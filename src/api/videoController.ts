// @ts-ignore
/* eslint-disable */
import request from "@/utils/request.ts";

/** 此处后端没有提供注释 GET /video */
export async function getAllVideo(options?: { [key: string]: any }) {
  return request<API.BaseResponse>("/video", {
    method: "GET",
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /video */
export async function updateVideoById(
  body: API.Video,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponse>("/video", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /video */
export async function createVideo(
  body: API.Video,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponse>("/video", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /video/${param0} */
export async function getVideoById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getVideoByIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/video/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 DELETE /video/${param0} */
export async function deleteVideoById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteVideoByIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/video/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}
