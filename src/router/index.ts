import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('../views/HomePage.vue'),
      meta: {
        title: '首页'
      }
    },
    {
      path: '/user/videos',
      name: 'UserVideos',
      component: () => import('../views/UserVideoPage.vue'),
      meta: {
        title: '视频学习'
      }
    },
    {
      path: '/admin/videos',
      name: 'AdminVideos',
      component: () => import('../views/AdminVideoPage.vue'),
      meta: {
        title: '视频管理'
      }
    },
    {
      path: '/video/:videoId',
      name: 'VideoPlayer',
      component: () => import('../views/VideoPlayerPage.vue'),
      meta: {
        title: '视频播放'
      }
    }
  ],
})

export default router
