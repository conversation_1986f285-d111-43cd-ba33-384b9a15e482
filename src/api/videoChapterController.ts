// @ts-ignore
/* eslint-disable */
import request from "@/utils/request.ts";

/** 此处后端没有提供注释 GET /videoChapter */
export async function getAllVideoChapter(options?: { [key: string]: any }) {
  return request<API.BaseResponse>("/videoChapter", {
    method: "GET",
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /videoChapter */
export async function updateVideoChapterById(
  body: API.VideoChapter,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponse>("/videoChapter", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /videoChapter */
export async function createVideoChapter(
  body: API.VideoChapter,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponse>("/videoChapter", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /videoChapter/${param0} */
export async function getVideoChapterById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getVideoChapterByIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/videoChapter/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 DELETE /videoChapter/${param0} */
export async function deleteVideoChapterById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteVideoChapterByIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/videoChapter/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}
