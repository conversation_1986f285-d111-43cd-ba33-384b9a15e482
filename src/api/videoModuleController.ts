// @ts-ignore
/* eslint-disable */
import request from "@/utils/request.ts";

/** 此处后端没有提供注释 GET /videoModule */
export async function getAllVideoModule(options?: { [key: string]: any }) {
  return request<API.BaseResponse>("/videoModule", {
    method: "GET",
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /videoModule */
export async function updateVideoModuleById(
  body: API.VideoModule,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponse>("/videoModule", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /videoModule */
export async function createVideoModule(
  body: API.VideoModule,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponse>("/videoModule", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /videoModule/${param0} */
export async function getVideoModuleById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getVideoModuleByIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/videoModule/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 DELETE /videoModule/${param0} */
export async function deleteVideoModuleById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteVideoModuleByIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/videoModule/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}
