// @ts-ignore
/* eslint-disable */
import request from "@/utils/request.ts";

/** 此处后端没有提供注释 GET /videoCourse */
export async function getAllVideoCourse(options?: { [key: string]: any }) {
  return request<API.BaseResponse>("/videoCourse", {
    method: "GET",
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 PUT /videoCourse */
export async function updateVideoCourseById(
  body: API.VideoCourse,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponse>("/videoCourse", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /videoCourse */
export async function createVideoCourse(
  body: API.VideoCourse,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponse>("/videoCourse", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /videoCourse/${param0} */
export async function getVideoCourseById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getVideoCourseByIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/videoCourse/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 DELETE /videoCourse/${param0} */
export async function deleteVideoCourseById(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteVideoCourseByIdParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.BaseResponse>(`/videoCourse/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}
