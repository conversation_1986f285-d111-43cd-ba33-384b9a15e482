import request from "@/services/request/request.ts";

// 课程数据结构
export interface Course {
  course_id: number;
  course_name: string;
  course_ai_summary?: string;
  is_delete: number;
  create_time: string;
  update_time: string;
}

// 课程列表响应
export interface CourseListRes {
  code: number;
  message: string;
  data: {
    list: Course[];
    total: number;
  };
}

// 课程详情响应
export interface CourseDetailRes {
  code: number;
  message: string;
  data: Course;
}

// 课程创建/更新请求
export interface CourseRequest {
  course_name: string;
  course_ai_summary?: string;
}

/**
 * 获取课程列表
 * @param params 查询参数
 * @returns 课程列表
 */
export function getCourseList(params?: {
  page?: number;
  pageSize?: number;
  keyword?: string;
}): Promise<CourseListRes> {
  return request.get('/api/course/list', { params });
}

/**
 * 获取课程详情
 * @param courseId 课程ID
 * @returns 课程详情
 */
export function getCourseDetail(courseId: number): Promise<CourseDetailRes> {
  return request.get(`/api/course/${courseId}`);
}

/**
 * 创建课程
 * @param data 课程数据
 * @returns 创建结果
 */
export function createCourse(data: CourseRequest): Promise<any> {
  return request.post('/api/course', data);
}

/**
 * 更新课程
 * @param courseId 课程ID
 * @param data 课程数据
 * @returns 更新结果
 */
export function updateCourse(courseId: number, data: CourseRequest): Promise<any> {
  return request.put(`/api/course/${courseId}`, data);
}

/**
 * 删除课程
 * @param courseId 课程ID
 * @returns 删除结果
 */
export function deleteCourse(courseId: number): Promise<any> {
  return request.delete(`/api/course/${courseId}`);
}
