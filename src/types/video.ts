// 课程数据结构
export interface Course {
  course_id: number;
  course_name: string;
  course_ai_summary?: string;
  is_delete: number;
  create_time: string;
  update_time: string;
}

// 模块数据结构
export interface Module {
  module_id: number;
  module_name: string;
  module_ai_summary?: string;
  is_delete: number;
  create_time: string;
  update_time: string;
}

// 章节数据结构
export interface Chapter {
  chapter_id: number;
  chapter_name: string;
  chapter_ai_summary?: string;
  is_delete: number;
  create_time: string;
  update_time: string;
}

// 视频文本数据结构
export interface VideoTextData {
  start_time: number;
  end_time: number;
  text: string;
}

// 视频数据结构
export interface Video {
  video_id: number;
  uploader_id: number;
  video_title: string;
  video_url?: string;
  original_video_name?: string;
  video_cover?: string;
  video_duration?: number;
  video_status: number; // 0上传中, 1处理中, 2完成, 3失败
  is_delete: number;
  create_time: string;
  update_time: string;
  text_data?: VideoTextData[];
  summary_data?: VideoTextData[];
  summary100?: string;
  // 关联数据
  course_name?: string;
  module_name?: string;
  chapter_name?: string;
}

// 课程-模块映射
export interface CourseModuleMapping {
  id: number;
  course_id: number;
  module_id: number;
  sort_order: number;
}

// 模块-章节映射
export interface ModuleChapterMapping {
  id: number;
  module_id: number;
  chapter_id: number;
  sort_order: number;
}

// 章节-视频映射
export interface ChapterVideoMapping {
  id: number;
  chapter_id: number;
  video_id: number;
  sort_order: number;
}

// 视频状态枚举
export enum VideoStatus {
  UPLOADING = 0,    // 上传中
  PROCESSING = 1,   // 处理中
  COMPLETED = 2,    // 完成
  FAILED = 3        // 失败
}

// 视频状态标签映射
export const VIDEO_STATUS_LABELS = {
  [VideoStatus.UPLOADING]: '上传中',
  [VideoStatus.PROCESSING]: '处理中',
  [VideoStatus.COMPLETED]: '已完成',
  [VideoStatus.FAILED]: '失败'
};

// 视频状态颜色映射
export const VIDEO_STATUS_COLORS = {
  [VideoStatus.UPLOADING]: 'blue',
  [VideoStatus.PROCESSING]: 'orange',
  [VideoStatus.COMPLETED]: 'green',
  [VideoStatus.FAILED]: 'red'
};

// AI视频总结数据结构
export interface VideoSummary {
  ai_video_summary_id: number;
  video_id: number;
  audio_id?: number;
  start_seconds: number;
  end_seconds: number;
  summary_content: string;
}

// 筛选条件接口
export interface VideoFilterParams {
  course_id?: number;
  module_id?: number;
  chapter_id?: number;
  video_status?: number;
  keyword?: string;
  page?: number;
  pageSize?: number;
}

// 上传表单数据
export interface VideoUploadForm {
  course_id?: number;
  module_id?: number;
  chapter_id?: number;
  video_title: string;
  video_file?: File;
}

// 视频路径信息
export interface VideoPath {
  course_name: string;
  module_name: string;
  chapter_name: string;
}
