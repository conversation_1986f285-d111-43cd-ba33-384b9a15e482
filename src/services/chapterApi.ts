import request from "@/services/request/request.ts";

// 章节数据结构
export interface Chapter {
  chapter_id: number;
  chapter_name: string;
  chapter_ai_summary?: string;
  is_delete: number;
  create_time: string;
  update_time: string;
}

// 模块-章节映射
export interface ModuleChapterMapping {
  id: number;
  module_id: number;
  chapter_id: number;
  sort_order: number;
}

// 章节列表响应
export interface ChapterListRes {
  code: number;
  message: string;
  data: {
    list: Chapter[];
    total: number;
  };
}

// 章节详情响应
export interface ChapterDetailRes {
  code: number;
  message: string;
  data: Chapter;
}

// 章节创建/更新请求
export interface ChapterRequest {
  chapter_name: string;
  chapter_ai_summary?: string;
}

/**
 * 获取章节列表
 * @param params 查询参数
 * @returns 章节列表
 */
export function getChapterList(params?: {
  page?: number;
  pageSize?: number;
  keyword?: string;
}): Promise<ChapterListRes> {
  return request.get('/api/chapter/list', { params });
}

/**
 * 根据模块ID获取章节列表
 * @param moduleId 模块ID
 * @returns 章节列表
 */
export function getChaptersByModule(moduleId: number): Promise<ChapterListRes> {
  return request.get(`/api/module/${moduleId}/chapters`);
}

/**
 * 获取章节详情
 * @param chapterId 章节ID
 * @returns 章节详情
 */
export function getChapterDetail(chapterId: number): Promise<ChapterDetailRes> {
  return request.get(`/api/chapter/${chapterId}`);
}

/**
 * 创建章节
 * @param data 章节数据
 * @returns 创建结果
 */
export function createChapter(data: ChapterRequest): Promise<any> {
  return request.post('/api/chapter', data);
}

/**
 * 更新章节
 * @param chapterId 章节ID
 * @param data 章节数据
 * @returns 更新结果
 */
export function updateChapter(chapterId: number, data: ChapterRequest): Promise<any> {
  return request.put(`/api/chapter/${chapterId}`, data);
}

/**
 * 删除章节
 * @param chapterId 章节ID
 * @returns 删除结果
 */
export function deleteChapter(chapterId: number): Promise<any> {
  return request.delete(`/api/chapter/${chapterId}`);
}

/**
 * 添加章节到模块
 * @param moduleId 模块ID
 * @param chapterId 章节ID
 * @param sortOrder 排序权重
 * @returns 添加结果
 */
export function addChapterToModule(moduleId: number, chapterId: number, sortOrder: number = 0): Promise<any> {
  return request.post('/api/module-chapter-mapping', {
    module_id: moduleId,
    chapter_id: chapterId,
    sort_order: sortOrder
  });
}

/**
 * 从模块中移除章节
 * @param moduleId 模块ID
 * @param chapterId 章节ID
 * @returns 移除结果
 */
export function removeChapterFromModule(moduleId: number, chapterId: number): Promise<any> {
  return request.delete(`/api/module-chapter-mapping/${moduleId}/${chapterId}`);
}
